# WebData失败告警功能

## 功能概述

当WebData任务失败次数达到配置的阈值时，系统会自动发送告警邮件到指定邮箱。

## 配置说明

### 1. 失败告警阈值 (failNoticeValve)
- 类型：整数
- 说明：当任务失败次数大于等于此值时触发告警
- 示例：设置为3，表示失败3次或以上时发送告警

### 2. 告警邮箱 (sendEmail)
- 类型：字符串
- 说明：接收告警邮件的邮箱地址，多个邮箱用分号(;)隔开
- 示例：`<EMAIL>;<EMAIL>`

## 使用步骤

### 1. 在WebData配置页面设置告警参数

1. 访问 `/webdata/conf` 页面
2. 新增或编辑WebData配置
3. 设置"失败告警阈值"字段
4. 设置"告警邮箱"字段
5. 保存配置

### 2. 告警触发条件

- 任务执行完成（状态为FINISHED）
- 失败次数 >= 失败告警阈值
- 配置了告警邮箱
- 当次执行未发送过告警（避免重复发送）

### 3. 告警邮件内容

告警邮件包含以下信息：
- 任务名称
- 任务代码
- 失败次数
- 告警阈值
- 执行时间
- 任务状态
- 总任务数
- 成功数
- 失败数

## 技术实现

### 数据库字段

#### t_web_data_conf表
- `fail_notice_valve`: 失败告警阈值
- `send_email`: 告警邮箱

#### t_web_data_conf_log表
- `is_send_notify`: 是否已发送告警标记

### 核心逻辑

1. 任务完成时调用 `WebDataServiceImpl.checkAndSendFailureAlert()`
2. 检查配置和失败次数
3. 发送告警邮件
4. 更新告警发送标记

### 邮件发送

使用 `AdminNotifyService.sendEmail()` 方法发送邮件，支持：
- 多个收件人（分号分隔）
- HTML格式内容
- 异常处理和日志记录

## 注意事项

1. 每次任务运行只发送一次告警邮件
2. 需要正确配置邮件服务器设置
3. 告警阈值应根据实际业务需求设置
4. 建议设置多个告警邮箱以确保及时收到通知

## 示例配置

```
任务名称: 股票数据抓取
失败告警阈值: 5
告警邮箱: <EMAIL>;<EMAIL>
```

当该任务失败5次或以上时，会向两个邮箱发送告警邮件。
