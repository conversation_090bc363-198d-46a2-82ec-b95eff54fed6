<style>
  .demo-table-expand {font-size: 0;}
  .demo-table-expand label {width: 200px;color: #99a9bf;}
  .demo-table-expand .el-form-item {margin-right: 0;margin-bottom: 0;width: 50%;}
</style>

<div id="app" v-cloak>

<el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>

<el-table :data="tableData" border stripe v-loading.body="tableLoading">
    <el-table-column type="expand">
        <template scope="props">
	        <el-form label-position="left" inline class="demo-table-expand">
	          <el-form-item label="起始值">
	            <span>{{ props.row.iStart }}</span>
	          </el-form-item>
	          <el-form-item label="结束值">
	            <span>{{ props.row.iEnd }}</span>
	          </el-form-item>
	          <el-form-item label="自定义序号生成类">
	            <span>{{ props.row.customSeqClassname }}</span>
	          </el-form-item>
	          <el-form-item label="自定义序号配置">
	            <span>{{ props.row.customSeqConf }}</span>
	          </el-form-item>
	          <el-form-item label="URL传参">
	            <span>{{ props.row.param }}</span>
	          </el-form-item>
	          <el-form-item label="线程数">
	            <span>{{ props.row.threads }}</span>
	          </el-form-item>
	          <el-form-item label="本地文件保存名称">
	            <span>{{ props.row.fileSave }}</span>
	          </el-form-item>
	          <el-form-item label="处理类名">
	            <span>{{ props.row.handlerClassname }}</span>
	          </el-form-item>
              <el-form-item label="编码">
                <span>{{ props.row.encode }}</span>
              </el-form-item>
              <el-form-item label="每次请求睡眠毫秒">
                <span>{{ props.row.sleepMsEach }}</span>
              </el-form-item>
              <el-form-item label="请求重试次数">
                <span>{{ props.row.httpRetryTimes }}</span>
              </el-form-item>
              <el-form-item label="失败告警阈值">
                <span>{{ props.row.failNoticeValve }}</span>
              </el-form-item>
              <el-form-item label="告警邮箱">
                <span>{{ props.row.sendEmail }}</span>
              </el-form-item>
	        </el-form>
        </template>
    </el-table-column>
    <el-table-column prop="name" label="名称" width="100"></el-table-column>
    <el-table-column prop="code" label="抓取代号" width="200"></el-table-column>
    <el-table-column prop="url" label="URL" width="300"></el-table-column>
    <el-table-column label="块regex" width="100">
        <template scope="scope">
            <el-popover title="解析regex" width="400" trigger="hover" v-if="scope.row.blockRegex">
                <el-button slot="reference" size="small">查看</el-button>
                <pre>{{scope.row.blockRegex}}</pre>
            </el-popover>
        </template>
    </el-table-column>
    <el-table-column label="解析regex" width="100">
        <template scope="scope">
            <el-popover title="解析regex" width="400" trigger="hover">
                <el-button slot="reference" size="small">查看</el-button>
                <pre>{{scope.row.parseRegex}}</pre>
            </el-popover>
        </template>
    </el-table-column>
    <el-table-column label="定时任务cron" width="220">
        <template scope="scope">
            <div>
                <el-switch
                    v-model="scope.row.enableCron"
                    active-color="#13ce66"
                    inactive-color="#999"
                    @change="updateEnableCron(scope.row)">
                </el-switch><br/>
                {{scope.row.cronExpression}}
            </div>
        </template>
    </el-table-column>
    <el-table-column label="状态" width="240">
      <template scope="scope">{{taskInfo[scope.row.id]}}
      </template>
    </el-table-column>
    <el-table-column label="操作" min-width="240">
      <template scope="scope">
        <el-button type="success" size="small" @click="handleAddOrEdit(false, scope.row)">修改</el-button>
        <el-button type="primary" size="small" @click="start(scope.row)">启动</el-button>
        <el-button type="danger" size="small" @click="stop(scope.row)">停止</el-button>
      </template>
    </el-table-column>
</el-table>

<el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
     :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper">
</el-pagination>

<el-dialog title="新增或修改" :visible.sync="showDialog" top="0">
    <el-form :model="addEditForm" label-position="right" label-width="200px" ref="addEditForm">
        <el-form-item label="名称" prop="name">
            <el-input v-model="addEditForm.name" placeholder="名称"></el-input>
        </el-form-item>
        <el-form-item label="抓取代号" prop="code">
            <el-input v-model="addEditForm.code" placeholder="抓取代号"></el-input>
        </el-form-item>
        <el-form-item label="URL" prop="url">
            <el-input v-model="addEditForm.url" placeholder="变量用${i}表示"></el-input>
        </el-form-item>
        <el-form-item label="起始值" prop="iStart">
            <el-input v-model="addEditForm.iStart" placeholder="数字"></el-input>
        </el-form-item>
        <el-form-item label="结束值" prop="iEnd">
            <el-input v-model="addEditForm.iEnd" placeholder="数字"></el-input>
        </el-form-item>
        <el-form-item label="参数" prop="param">
            <el-input v-model="addEditForm.param" placeholder="json格式"></el-input>
        </el-form-item>
        <el-form-item label="自定义序号生成类" prop="customSeqClassname">
            <el-input v-model="addEditForm.customSeqClassname" placeholder="customSeqClassname"></el-input>
        </el-form-item>
        <el-form-item label="自定义序号配置json字符串" prop="customSeqConf">
            <el-input v-model="addEditForm.customSeqConf" placeholder="customSeqConf"></el-input>
        </el-form-item>
        <el-form-item label="分块正则" prop="blockRegex">
            <el-input v-model="addEditForm.blockRegex" type="textarea" :rows="3"></el-input>
        </el-form-item>
        <el-form-item label="解析正则" prop="parseRegex">
            <el-input v-model="addEditForm.parseRegex" type="textarea" :rows="8"></el-input>
        </el-form-item>
        <el-form-item label="保存本地文件" prop="fileSave">
            <el-input v-model="addEditForm.fileSave" placeholder="变量用${i}表示"></el-input>
        </el-form-item>
        <el-form-item label="处理类名" prop="handlerClassname">
            <el-input v-model="addEditForm.handlerClassname" placeholder="类全称"></el-input>
        </el-form-item>
        <el-form-item label="线程数" prop="threads">
            <el-input v-model="addEditForm.threads" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="encode">
            <el-input v-model="addEditForm.encode" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="是否启用定时任务" prop="enableCron">
            <el-switch v-model="addEditForm.enableCron"></el-switch>
        </el-form-item>
        <el-form-item label="定时任务cron" prop="cronExpression">
            <el-input v-model="addEditForm.cronExpression" placeholder="Spring的cron表达式，精确到秒"></el-input>
        </el-form-item>
        <el-form-item label="每次请求睡眠毫秒" prop="sleepMsEach">
            <el-input v-model="addEditForm.sleepMsEach" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="请求重试次数" prop="httpRetryTimes">
            <el-input v-model="addEditForm.httpRetryTimes" placeholder=""></el-input>
        </el-form-item>
        <el-form-item label="失败告警阈值" prop="failNoticeValve">
            <el-input v-model="addEditForm.failNoticeValve" placeholder="失败次数达到此值时发送告警"></el-input>
        </el-form-item>
        <el-form-item label="告警邮箱" prop="sendEmail">
            <el-input v-model="addEditForm.sendEmail" placeholder="多个邮箱用分号隔开"></el-input>
        </el-form-item>
    </el-form>
    <div slot="footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="doAddOrEdit">确 定</el-button>
    </div>
</el-dialog>

</div>

<script>
var defaultQueryForm = {page: 1, pageSize: 10}
var defaultAddForm = {}
var vm = new Vue({
	el: '#app',
	data: {
		queryForm: Utils.copy(defaultQueryForm),
		addEditForm: Utils.copy(defaultAddForm),
        total: 0,
        tableData: [],
        tableLoading: false,
        taskInfo: {},
        showDialog: false
	},
    created: function() {
    	this.getData();
        setTimeout(this.updateTaskInfo, 1000) // 等待getData返回数据
    },
    methods: {
    	getData: function() {
    		var that = this;
    		that.tableLoading = true;
    		Resource.post("${_contextPath_}/webdata/configs", this.queryForm, function(resp){
				that.tableData = resp.data.data;
				that.total = resp.data.total;
                that.tableLoading = false;
    		});
    	},
    	pageChange: function(page) {
    		this.queryForm.page = page;
    		this.getData();
    	},
    	updateTaskInfo: function() {
    		var that = this;
    		for(var i = 0; i < that.tableData.length; i++) {
        		Resource.get("${_contextPath_}/webdata/task_info", {webDataConfId:
        			that.tableData[i].id}, function(resp) {
        			that.$set(that.taskInfo, resp.data.id, resp.data.info);
        		});
    		}
    		setTimeout(that.updateTaskInfo, 3000);
    	},
    	start: function(row) {
    		Resource.get("${_contextPath_}/webdata/start_task", {webDataConfId: row.id}, function(resp) {
    			console.log(resp)
    		});
    	},
    	stop: function(row) {
    		Resource.get("${_contextPath_}/webdata/stop_task", {webDataConfId: row.id}, function(resp) {
                if (resp.data.success) {
                    Message.success("停止成功，具体信息:" + resp.data.message)
                } else {
                    Message.error("停止失败，具体信息:" + resp.data.message)
                }
    		});
    	},
    	updateEnableCron: function(row) {
    	    var that = this;
    	    Resource.post("${_contextPath_}/webdata/add_or_edit_conf", row, function(resp){
                Message.success("更新成功");
    	    });
    	},
        handleAddOrEdit: function(isAdd, row) {
        	this.showDialog = true
        	Form.clearError(this, 'addEditForm')
        	this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
        },
    	doAddOrEdit: function() {
    	    var that = this, formName = 'addEditForm';
    	    var isEdit = this.addEditForm.id ? true : false;
            Form.validate(this, formName, function() {
                Resource.post("${_contextPath_}/webdata/add_or_edit_conf", that.addEditForm, function(resp){
                    Message.success(isEdit ? "修改成功" : "新增成功");
                    that.showDialog = false
                    that.getData();
                });
            });
    	}
    }
});
</script>