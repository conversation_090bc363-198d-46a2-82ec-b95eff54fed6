package com.pugwoo.rainbow.webdata.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("t_web_data_conf_log")
public class WebDataConfLogDO extends AdminCoreDO {

    /** t_web_data_conf的id<br/>Column: [web_data_conf_id] */
    @Column(value = "web_data_conf_id")
    private Long webDataConfId;

    /** 任务状态<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 执行开始时间<br/>Column: [begin_time] */
    @Column(value = "begin_time")
    private Date beginTime;

    /** 执行结束时间<br/>Column: [end_time] */
    @Column(value = "end_time")
    private Date endTime;

    /** 成功的任务数<br/>Column: [success_count] */
    @Column(value = "success_count")
    private Integer successCount;

    /** 失败的任务数<br/>Column: [fail_count] */
    @Column(value = "fail_count")
    private Integer failCount;

    /** 任务总数<br/>Column: [total_count] */
    @Column(value = "total_count")
    private Integer totalCount;

    /** 是否已经发送过告警<br/>Column: [is_send_notify] */
    @Column(value = "is_send_notify")
    private Boolean isSendNotify;
}