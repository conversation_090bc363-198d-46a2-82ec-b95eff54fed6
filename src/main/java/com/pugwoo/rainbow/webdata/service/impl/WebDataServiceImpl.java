package com.pugwoo.rainbow.webdata.service.impl;

import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.rainbow.webdata.entity.WebDataConfLogDO;
import com.pugwoo.rainbow.webdata.entity.WebDataConfigDO;
import com.pugwoo.rainbow.webdata.service.GetDataTask;
import com.pugwoo.rainbow.webdata.service.IWebDataService;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import com.pugwoo.wooutils.task.EasyRunTask;
import com.pugwoo.wooutils.task.TaskResult;
import com.pugwoo.wooutils.task.TaskStatusEnum;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;

@Service
public class WebDataServiceImpl implements IWebDataService, ApplicationContextAware {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(WebDataServiceImpl.class);

	@Resource
	private DBHelper dbHelper;

	@Autowired
	private AdminNotifyService adminNotifyService;

	/**
	 * 由IWebDataService管理的任务，taskCode -> EasyRunTask对象
	 */
	private final Map<Long, EasyRunTask> tasks = new ConcurrentHashMap<>();

	/**
	 * webDataConfigId -> 当前webDataConfigLogId
	 */
	private final Map<Long, Long> lastTaskLogId = new ConcurrentHashMap<>();

	private final ThreadPoolExecutor webLogThreadPool =
			ThreadPoolUtils.createThreadPool(1, 1000, 1, "WebDataLog");

	private ApplicationContext applicationContext;

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

	@Override
	public PageData<WebDataConfigDO> getDataConfigPageData(int page, int pageSize) {
		return dbHelper.getPage(WebDataConfigDO.class, page, pageSize);
	}

	/**
	 * 检查配置参数
	 */
	private boolean checkConfig(WebDataConfigDO webDataConfig, Long webDataConfId) {
		if(webDataConfig == null) {
			LOGGER.error("webDataConfig not exist, id:{}", webDataConfId);
			return false;
		}

		if(StringUtils.isBlank(webDataConfig.getUrl())) {
			LOGGER.error("webDataConfig wrong url, id:{}", webDataConfig.getId());
			return false;
		}

		return true;
	}

	@Synchronized(throwExceptionIfNotGetLock = false)
	@Override
	public TaskResult runTask(Long webDataConfId) {
		if(webDataConfId == null) {
			return null;
		}
		WebDataConfigDO webDataConfig = dbHelper.getByKey(WebDataConfigDO.class, webDataConfId);
		if(!checkConfig(webDataConfig, webDataConfId)) {
			return null;
		}

		EasyRunTask task = tasks.get(webDataConfId);
		if(task != null) {
			if(task.getStatus() == TaskStatusEnum.RUNNING || task.getStatus() == TaskStatusEnum.STOPPING) {
				return new TaskResult(false, "当前任务还没结束，不能开始");
			}
		}

		GetDataTask runtask = applicationContext.getBean(GetDataTask.class, webDataConfig);

		int threads = 1;
		if(webDataConfig.getThreads() != null && webDataConfig.getThreads() > 1) {
			threads = webDataConfig.getThreads();
		}

		task = new EasyRunTask(runtask, threads);

		WebDataConfLogDO logDO = new WebDataConfLogDO();
		logDO.setWebDataConfId(webDataConfId);
		logDO.setBeginTime(new Date());
		logDO.setStatus(TaskStatusEnum.NEW.getCode());
		dbHelper.insert(logDO);

		tasks.put(webDataConfId, task);
		lastTaskLogId.put(webDataConfId, logDO.getId());

		return task.start();
	}

	@Override
	public EasyRunTask getTask(Long webDataConfId) {
		return tasks.get(webDataConfId);
	}

	@Override
	public TaskResult stopTask(Long webDataConfId) {
		EasyRunTask task = tasks.get(webDataConfId);
		if(task != null) {
			TaskResult result = task.stop();
			if (result.isSuccess()) {
				Long logId = lastTaskLogId.get(webDataConfId);
				if (logId != null) {
					WebDataConfLogDO logDO = dbHelper.getByKey(WebDataConfLogDO.class, logId);
					if (logDO != null) {
						logDO.setStatus(TaskStatusEnum.STOPPING.getCode());
						logDO.setEndTime(new Date());
						dbHelper.update(logDO);
					}
				}
			}
			return result;
		}
		TaskResult taskResult = new TaskResult(true, "任务不存在");
		taskResult.setCode("0");
		return taskResult;
	}

	@Override
	public Long addOrUpdate(WebDataConfigDO webDataConfigDO) {
	    dbHelper.insertOrUpdate(webDataConfigDO);
	    return webDataConfigDO.getId();
	}

	@PostConstruct
    public void updateLog() {
		webLogThreadPool.submit(new Runnable() {
			@Override
			public void run() {
				while (true) {
					try {
						Thread.sleep(3000);
						for (Map.Entry<Long, Long> e : lastTaskLogId.entrySet()) {
							WebDataConfLogDO logDO = dbHelper.getByKey(WebDataConfLogDO.class, e.getValue());
							if (logDO == null) {
								continue;
							}

							EasyRunTask easyRunTask = tasks.get(e.getKey());
							if (easyRunTask != null) {
								// 检查是否需要发送失败告警
								checkAndSendFailureAlert(e.getKey(), logDO);

								logDO.setSuccessCount(easyRunTask.getSuccess());
								logDO.setFailCount(easyRunTask.getFail());
								logDO.setTotalCount(easyRunTask.getTotal());
								logDO.setStatus(easyRunTask.getStatus().getCode());
								if (TaskStatusEnum.FINISHED == easyRunTask.getStatus()
										|| TaskStatusEnum.STOPPED == easyRunTask.getStatus()) {
									logDO.setEndTime(new Date());
								}
								dbHelper.update(logDO);

								// 如果已经成功结束了，那么清理掉；如果是STOPPED，咱不清理，其实可能还有恢复功能，概率小，不处理
								if (TaskStatusEnum.FINISHED == easyRunTask.getStatus()) {
									lastTaskLogId.remove(e.getKey());
								}
							}
						}
					} catch (Throwable e) {
						LOGGER.error("updateLog error", e);
					}
				}
			}
		});
	}

	@Override
	public WebDataConfLogDO getLatestLog(Long webDataConfId) {
		if (webDataConfId == null) {
			return null;
		}
		return dbHelper.getOne(WebDataConfLogDO.class,
				"where web_data_conf_id=? order by id desc limit 1", webDataConfId);
	}

	@Override
	public void checkAndSendFailureAlert(Long webDataConfId, WebDataConfLogDO logDO) {
		try {
			// 获取配置信息
			WebDataConfigDO config = dbHelper.getByKey(WebDataConfigDO.class, webDataConfId);
			if (config == null || config.getFailNoticeValve() == null || config.getFailNoticeValve() <= 0) {
				return;
			}

			// 检查是否配置了告警邮箱
			if (StringTools.isBlank(config.getSendEmail())) {
				return;
			}

			// 检查失败次数是否达到阈值
			Integer failCount = logDO.getFailCount();
			if (failCount == null || failCount < config.getFailNoticeValve()) {
				return;
			}

			// 检查是否已经发送过告警（避免重复发送）
			if (logDO.getIsSendNotify() != null && logDO.getIsSendNotify()) {
				return;
			}

			// 发送告警邮件
			String title = String.format("WebData任务失败告警 - %s", config.getName());
			String content = String.format(
				"任务名称：%s\n" +
				"任务代码：%s\n" +
				"失败次数：%d\n" +
				"告警阈值：%d\n" +
				"执行时间：%s\n" +
				"任务状态：%s\n" +
				"总任务数：%d\n" +
				"成功数：%d\n" +
				"失败数：%d",
				config.getName(),
				config.getCode(),
				failCount,
				config.getFailNoticeValve(),
				logDO.getBeginTime() != null ? DateUtils.format(logDO.getBeginTime()) : "未知",
				logDO.getStatus(),
				logDO.getTotalCount() != null ? logDO.getTotalCount() : 0,
				logDO.getSuccessCount() != null ? logDO.getSuccessCount() : 0,
				failCount
			);

			adminNotifyService.sendEmail(config.getSendEmail(), title, content);
			LOGGER.info("发送WebData失败告警邮件成功，任务ID：{}，失败次数：{}", webDataConfId, failCount);

			// 标记已发送告警
			logDO.setIsSendNotify(true);
			dbHelper.update(logDO);

		} catch (Exception e) {
			LOGGER.error("发送WebData失败告警邮件失败，任务ID：{}", webDataConfId, e);
		}
	}
}
