package com.pugwoo.rainbow.webdata.service;

import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.rainbow.webdata.entity.WebDataConfLogDO;
import com.pugwoo.rainbow.webdata.entity.WebDataConfigDO;
import com.pugwoo.wooutils.task.EasyRunTask;
import com.pugwoo.wooutils.task.TaskResult;

/**
 * 抓取互联网数据相关服务
 * <AUTHOR>
 */
public interface IWebDataService {

	/**
	 * 启动任务
	 */
	TaskResult runTask(Long webDataConfId);
	
	/**
	 * 停止任务
	 */
	TaskResult stopTask(Long webDataConfId);
	
	/**
	 * 查询任务状态，拿到的这个任务，尽量不要手动启动或停止它。
	 * @return 不存在返回null
	 */
	EasyRunTask getTask(Long webDataConfId);

	/**
	 * 查询抓取配置表
	 */
	PageData<WebDataConfigDO> getDataConfigPageData(int page, int pageSize);
	
	/**
	 * 新增或修改
	 */
	Long addOrUpdate(WebDataConfigDO webDataConfigDO);


	WebDataConfLogDO getLatestLog(Long webDataConfId);

	/**
	 * 检查并发送失败告警邮件
	 */
	void checkAndSendFailureAlert(Long webDataConfId, WebDataConfLogDO logDO);
}
