package com.pugwoo.finance.reminder.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum PriceReminderMatchTypeEnum {

	/**
	 * 这里指蜡烛图的细的那段线
	 */
	THIN("THIN", "高低价"),

	/**
	 * 这里指蜡烛图的粗的那段线
	 */
	FAT("FAT", "开收盘"),

	/**
	 * 超过上涨百分比
	 */
	BEYOND_RISE_PCT("BEYOND_RISE_PCT", "超过上涨百分比"),

	/**
	 * 超过下跌百分比
	 */
	BELOW_FALL_PCT("BELOW_FALL_PCT", "超过下跌百分比"),


	LOW_PROBABILITY_RISE("LOW_PROBABILITY_RISE", "低概率上涨"),

	LOW_PROBABILITY_FALL("LOW_PROBABILITY_FALL", "低概率下跌"),

	VERY_LOW_PROBABILITY_RISE("VERY_LOW_PROBABILITY_RISE", "极低概率上涨"),

	VERY_LOW_PROBABILITY_FALL("VERY_LOW_PROBABILITY_FALL", "极低概率下跌"),

	;

	private final String code;

	private final String name;

	PriceReminderMatchTypeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public static PriceReminderMatchTypeEnum getByCode(String code) {
		for (PriceReminderMatchTypeEnum e : PriceReminderMatchTypeEnum.values()) {
			if (Objects.equals(e.getCode(), code)) {
				return e;
			}
		}
		return null;
	}

	public static String getNameByCode(String code) {
		PriceReminderMatchTypeEnum e = getByCode(code);
		return e == null ? code : e.getName();
	}
}
