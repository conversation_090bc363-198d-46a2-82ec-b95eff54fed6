package com.pugwoo.finance.reminder.job;

import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.finance.data.entity.SymbolChinaDO;
import com.pugwoo.finance.data.model.DailyDataVO;
import com.pugwoo.finance.data.model.RealtimeDataVO;
import com.pugwoo.finance.data.service.IStockInfoService;
import com.pugwoo.finance.data.service.LocalDailyData;
import com.pugwoo.finance.data.service.RealtimeData;
import com.pugwoo.finance.reminder.dto.PriceReminderMailDTO;
import com.pugwoo.finance.reminder.dto.QueryPriceReminderMatchReq;
import com.pugwoo.finance.reminder.entity.PriceReminderDO;
import com.pugwoo.finance.reminder.enums.PriceReminderMatchTypeEnum;
import com.pugwoo.finance.reminder.service.IPriceReminderService;
import com.pugwoo.finance.reminder.vo.PriceReminderMatchWithInfoVO;
import com.pugwoo.finance.utils.Env;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 需求：每隔一段时间（准实时）获取关注的股票当前价格，匹配则发送邮件<br/>
 * 1. 当前价格匹配<br/>
 * 2. 获取时最高最低价格匹配<br/>
 * @date 2018-07-20
 */
@Slf4j
@Component
public class PriceReminderRealtimeTask {

	@Value("${finance.normalFinanceNotifyEmail:}")
	private String normalFinanceNotifyEmail;
	@Value("${finance.importantFinanceNotifyEmail:}")
	private String importantFinanceNotifyEmail;

	@Autowired
	private IPriceReminderService priceReminderService;
	@Autowired
	private IStockInfoService stockInfoService;
	@Autowired
	private AdminNotifyService notifyService;

	/**
	 * 1分钟执行一次，监控关注的股票上涨下跌比例
	 */
	@Scheduled(fixedDelay = 1000 * 60)
	public void runWatchPercentage() {
		if (!isRun()) {
			return;
		}

		// 先处理上涨监控
		List<PriceReminderDO> riseReminderList = priceReminderService.getReminderForRisePctWithoutMatchToday();
		for (PriceReminderDO priceReminder : riseReminderList) {
			String symbol = priceReminder.getSymbol();
			RealtimeDataVO realtimeDataVO = RealtimeData.queryRealtimePrice(symbol);;
			if (realtimeDataVO == null) {
				continue;
			}
			DailyDataVO yesterdayData = getYesterdayData(symbol, LocalDate.now());
			if (yesterdayData == null || yesterdayData.getClose() == 0) {
				continue;
			}

			// 匹配今天的涨幅
			double risePct = (realtimeDataVO.getCurrentPrice() - yesterdayData.getClose()) / yesterdayData.getClose() * 100;
			boolean isMatch = false;
			boolean isTodayMatch = false;

			if (risePct > 0 && risePct >= priceReminder.getRisePercent().doubleValue()) {
			    isMatch = true;
				isTodayMatch = true;
			} else {
				// 如果今天没有match到，则加多1天看看，最长2天，不应该再长了，不然就有点违背完全随机理论了
				DailyDataVO dayBeforeYesterdayData = getDayBeforeYesterdayData(symbol, LocalDate.now());
				if (dayBeforeYesterdayData != null && dayBeforeYesterdayData.getClose() != 0) {
					double risePct2 = (realtimeDataVO.getCurrentPrice() - dayBeforeYesterdayData.getClose()) / dayBeforeYesterdayData.getClose() * 100;
					if (risePct2 > 0 && risePct2 >= priceReminder.getRisePercent().doubleValue()) {
						isMatch = true;
					}
				}
			}

			if (isMatch) {
				// 查询最后3次的匹配，用于发送邮件和判断是否昨天已经发送过了
				QueryPriceReminderMatchReq req = new QueryPriceReminderMatchReq();
				req.setPageSize(5);
				req.setSymbol(symbol);
				List<PriceReminderMatchWithInfoVO> lastThree = priceReminderService.getAllPriceMatch(req).getData();

				boolean isContinueSend = true;
				if (!isTodayMatch) {
					// 看看昨天是否发送过，如果发送过，则不再往下处理了
					if (!lastThree.isEmpty() &&
							DateUtils.formatDate(lastThree.getFirst().getMatchDate()).equals(LocalDate.now().minusDays(1).toString())) {
						isContinueSend = false;
					}
				}

				if (isContinueSend) {
					priceReminderService.addPriceMatch(symbol, new Date(), PriceReminderMatchTypeEnum.BEYOND_RISE_PCT,
							priceReminder, yesterdayData, realtimeDataVO.getCurrentPrice(), risePct, isTodayMatch);
					SymbolChinaDO bySymbol = stockInfoService.getBySymbol(symbol);

					// 发送邮件通知
					SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String title = "实时监控-涨幅监控(" + df.format(new Date()) + ")";

					StringBuilder mailContent = new StringBuilder("股票:" + symbol +
							"(" + (bySymbol == null ? "未知名称" : bySymbol.getName()) + ")," +
							"当前价格:" + realtimeDataVO.getCurrentPrice() + "," +
							"当前涨幅:" + String.format("%.2f", risePct) + "%" + (isTodayMatch ? "" : "[2天匹配]"));

					for (PriceReminderMatchWithInfoVO v : lastThree) {
						mailContent.append("\n").append(DateUtils.formatDate(v.getMatchDate()))
								.append(",").append(v.getCurChangePercent()).append("%,")
								.append(NumberUtils.roundUp(v.getCurPrice(), 2));
					}

					try {
						log.info("start send mail:{}", mailContent.toString());
						notifyService.sendEmail(getSendEmail(priceReminder), title, mailContent.toString());
						log.info("send mail succ");
					} catch (Exception e) {
						log.error("send mail fail", e);
						return;
					}
				}
			}
		}

		// 再处理下跌监控
		List<PriceReminderDO> fallReminderList = priceReminderService.getReminderForFallPctWithoutMatchToday();
		for (PriceReminderDO priceReminder : fallReminderList) {
			String symbol = priceReminder.getSymbol();
			RealtimeDataVO realtimeDataVO = RealtimeData.queryRealtimePrice(symbol);;
			if (realtimeDataVO == null) {
				continue;
			}
			DailyDataVO yesterdayData = getYesterdayData(symbol, LocalDate.now());
			if (yesterdayData == null || yesterdayData.getClose() == 0) {
				continue;
			}

			// 匹配
			double fallPct = (realtimeDataVO.getCurrentPrice() - yesterdayData.getClose()) / yesterdayData.getClose() * 100;
			boolean isMatch = false;
			boolean isTodayMatch = false;

            if (fallPct < 0 && -fallPct >= priceReminder.getFallPercent().doubleValue()) {
				isMatch = true;
				isTodayMatch = true;
			} else {
				// 如果今天没有match到，则加多1天看看，最长2天，不应该再长了，不然就有点违背完全随机理论了
				DailyDataVO dayBeforeYesterdayData = getDayBeforeYesterdayData(symbol, LocalDate.now());
				if (dayBeforeYesterdayData != null && dayBeforeYesterdayData.getClose() != 0) {
					double fallPct2 = (realtimeDataVO.getCurrentPrice() - dayBeforeYesterdayData.getClose()) / dayBeforeYesterdayData.getClose() * 100;
					if (fallPct2 < 0 && -fallPct2 >= priceReminder.getFallPercent().doubleValue()) {
						isMatch = true;
					}
				}
			}


			if (isMatch) {

				// 查询最后3次的匹配，用于发送邮件和判断是否昨天已经发送过了
				QueryPriceReminderMatchReq req = new QueryPriceReminderMatchReq();
				req.setPageSize(5);
				req.setSymbol(symbol);
				List<PriceReminderMatchWithInfoVO> lastThree = priceReminderService.getAllPriceMatch(req).getData();

				boolean isContinueSend = true;
				if (!isTodayMatch) {
					// 看看昨天是否发送过，如果发送过，则不再往下处理了
					if (!lastThree.isEmpty() &&
							DateUtils.formatDate(lastThree.getFirst().getMatchDate()).equals(LocalDate.now().minusDays(1).toString())) {
						isContinueSend = false;
					}
				}

				if (isContinueSend) {
					priceReminderService.addPriceMatch(symbol, new Date(), PriceReminderMatchTypeEnum.BELOW_FALL_PCT,
							priceReminder, yesterdayData, realtimeDataVO.getCurrentPrice(), fallPct, isTodayMatch);
					SymbolChinaDO bySymbol = stockInfoService.getBySymbol(symbol);

					// 发送邮件通知
					SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String title = "实时监控-跌幅监控(" + df.format(new Date()) + ")";

					StringBuilder mailContent = new StringBuilder("股票:" + symbol +
							"(" + (bySymbol == null ? "未知名称" : bySymbol.getName()) + ")," +
							"当前价格:" + realtimeDataVO.getCurrentPrice() + "," +
							"当前跌幅:" + String.format("%.2f", fallPct) + "%" + (isTodayMatch ? "" : "[2天匹配]"));

					for (PriceReminderMatchWithInfoVO v : lastThree) {
						mailContent.append("\n").append(DateUtils.formatDate(v.getMatchDate()))
								.append(",").append(v.getCurChangePercent()).append("%,")
								.append(NumberUtils.roundUp(v.getCurPrice(), 2));
					}

					try {
						log.info("start send mail:{}", mailContent);
						notifyService.sendEmail(getSendEmail(priceReminder), title, mailContent.toString());
						log.info("send mail succ");
					} catch (Exception e) {
						log.error("send mail fail", e);
						return;
					}
				}
			}
		}
	}

	/**
	 * 1分钟执行一次，监控关注的股票区间
	 */
	@Scheduled(fixedDelay = 1000 * 60)
	public void run() {
		if (!isRun()) {
			return;
		}
		log.info("start PriceReminderMailTask");

		// 获取今日未发送邮件的关注的symbolList
		List<PriceReminderDO> priceReminderList = priceReminderService.getReminderRangeWithoutSendMailToday();

		List<PriceReminderMailDTO> priceReminderMailDTOList = new ArrayList<>();
		List<PriceReminderMailDTO> specialPriceReminderMailDTOList = new ArrayList<>();

		// 循环获取价格并匹配
		for (PriceReminderDO priceReminder : priceReminderList) {
			String symbol = priceReminder.getSymbol();
			RealtimeDataVO realtimeDataVO = RealtimeData.queryRealtimePrice(symbol);;
			if (realtimeDataVO == null) {
				continue;
			}

			List<Map<String, Object>> msgs = new ArrayList<>();

			// 匹配
			if (priceReminder.getTargetLow() != null && priceReminder.getTargetHigh() != null) {
				double targetLow = priceReminder.getTargetLow().doubleValue();
				double targetHigh = priceReminder.getTargetHigh().doubleValue();
				String msg = null;
				if (targetLow <= realtimeDataVO.getCurrentPrice() && realtimeDataVO.getCurrentPrice() <= targetHigh) {  // 当前价格校验
					msg = "当前区间1";
				} else {
					if (targetLow <= realtimeDataVO.getHigh() && targetHigh >= realtimeDataVO.getLow()) {     // 今日价格检验
						msg = "今日区间1";
					}
				}
				if (StringUtils.isNotBlank(msg)) {
					Map<String, Object> map = MapUtils.of("msg", msg,
							"targetLow", targetLow, "targetHigh", targetHigh);
					msgs.add(map);
				}

			}
			if (priceReminder.getTargetLow2() != null && priceReminder.getTargetHigh2() != null) {
				double targetLow = priceReminder.getTargetLow2().doubleValue();
				double targetHigh = priceReminder.getTargetHigh2().doubleValue();
				String msg = null;
				if (targetLow <= realtimeDataVO.getCurrentPrice() && realtimeDataVO.getCurrentPrice() <= targetHigh) {  // 当前价格校验
					msg = "当前区间2";
				} else {
					if (targetLow <= realtimeDataVO.getHigh() && targetHigh >= realtimeDataVO.getLow()) {     // 今日价格检验
						msg = "今日区间2";
					}
				}
				if (StringUtils.isNotBlank(msg)) {
					Map<String, Object> map = MapUtils.of("msg", msg,
							"targetLow", targetLow, "targetHigh", targetHigh);
					msgs.add(map);
				}
			}

			if (!msgs.isEmpty()) {
				for (Map<String, Object> msg : msgs) {
					PriceReminderMailDTO priceReminderMailDTO = new PriceReminderMailDTO();
					priceReminderMailDTO.setSymbol(symbol);
					SymbolChinaDO bySymbol = stockInfoService.getBySymbol(symbol);
					priceReminderMailDTO.setName(bySymbol == null ? "未知名称" : bySymbol.getName());
					priceReminderMailDTO.setCurrentPrice(new BigDecimal(Double.toString(realtimeDataVO.getCurrentPrice())));
					priceReminderMailDTO.setTodatLow(new BigDecimal(Double.toString(realtimeDataVO.getLow())));
					priceReminderMailDTO.setTodayHigh(new BigDecimal(Double.toString(realtimeDataVO.getHigh())));
					priceReminderMailDTO.setTargetLow(new BigDecimal(Double.toString((Double) msg.get("targetLow"))));
					priceReminderMailDTO.setTargetHigh(new BigDecimal(Double.toString((Double) msg.get("targetHigh"))));
					priceReminderMailDTO.setMsg(String.valueOf(msg.get("msg")));

					if (priceReminder.getSpecialReminder() != null && priceReminder.getSpecialReminder()) {
						specialPriceReminderMailDTOList.add(priceReminderMailDTO);
					} else {
						priceReminderMailDTOList.add(priceReminderMailDTO);
					}
				}
			}
		}

		// sendMessage 这里等于是批量发送
		send(priceReminderMailDTOList, normalFinanceNotifyEmail);
		send(specialPriceReminderMailDTOList, importantFinanceNotifyEmail);
	}

	private void send(List<PriceReminderMailDTO> list, String sendTo) {
		if (!list.isEmpty()) {
			SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String title = "实时监控-匹配中关注股票(" + df.format(new Date()) + ")";

			StringBuilder mailContent = new StringBuilder();
			for(PriceReminderMailDTO p : list) {
				mailContent.append("<p>").append(p.getSymbol());
				mailContent.append("(").append(p.getName()).append("),");
				mailContent.append("当前价格:").append(p.getCurrentPrice()).append(",");
				mailContent.append("关注价格:").append(p.getTargetLow()).append("~").append(p.getTargetHigh()).append(",");
				mailContent.append("当前今日价格:").append(p.getTodatLow()).append("~").append(p.getTodayHigh()).append(",");
				mailContent.append("信息:").append(p.getMsg());
				mailContent.append("</p>");
			}

			try {
				log.info("start send mail:{}", mailContent.toString());
				notifyService.sendEmail(sendTo, title, mailContent.toString());
				log.info("send mail succ");
			} catch (Exception e) {
				log.error("send mail fail", e);
				return;
			}
		}
	}

	/**
	 * 获得昨天的股票数据
	 */
	private static DailyDataVO getYesterdayData(String symbol, LocalDate today) {
		if (StringTools.isBlank(symbol)) {
			return null;
		}
		LocalDate yesterday = today.minusDays(1);
		List<DailyDataVO> query = LocalDailyData.query(symbol,
				1, null, DateUtils.parse(DateUtils.formatDate(yesterday)));
		return ListUtils.isEmpty(query) ? null : query.getFirst();
	}

	private static DailyDataVO getDayBeforeYesterdayData(String symbol, LocalDate today) {
		if (StringTools.isBlank(symbol)) {
			return null;
		}
		LocalDate yesterday = today.minusDays(1);
		List<DailyDataVO> query = LocalDailyData.query(symbol,
				2, null, DateUtils.parse(DateUtils.formatDate(yesterday)));
		return query.size() != 2 ? null : query.get(1);
	}

	/**
	 * 判断当前是否运行，目前只适用于国内股票
	 */
	private boolean isRun() {
		// 只在linux服务器上跑
		if(Env.isWindows()) {
			return false;
		}
		Date now = new Date();
		int hour = Integer.parseInt(DateUtils.format(now, "HH"));
		int minute = Integer.parseInt(DateUtils.format(now, "mm"));
		int time = hour * 100 + minute;
		boolean isCloseTime = time < 925 || (1130 < time && time < 1300) || 1500 < time;
		if (isCloseTime) {
			return false; //抓取时间  09:25~11:30  13:00~15:00
		}

		// 只在周一到周五抓取，这里就不考虑法定节假日了
		int week = LocalDate.now().getDayOfWeek().getValue();
        return week != 6 && week != 7;
    }

	private String getSendEmail(PriceReminderDO priceReminderDO) {
		if (priceReminderDO.getSpecialReminder() != null && priceReminderDO.getSpecialReminder()) {
			return importantFinanceNotifyEmail;
		} else {
			return normalFinanceNotifyEmail;
		}
	}

}
