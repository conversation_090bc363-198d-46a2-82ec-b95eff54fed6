package com.pugwoo.finance.reminder.service;

import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.finance.data.model.DailyDataVO;
import com.pugwoo.finance.reminder.dto.QueryPriceReminderMatchReq;
import com.pugwoo.finance.reminder.entity.PriceReminderDO;
import com.pugwoo.finance.reminder.enums.PriceReminderMatchTypeEnum;
import com.pugwoo.finance.reminder.vo.PriceReminderIndustryCountVO;
import com.pugwoo.finance.reminder.vo.PriceReminderMatchWithInfoVO;
import com.pugwoo.finance.reminder.vo.PriceReminderWithInfoVO;
import com.pugwoo.finance.reminder.vo.PriceReminderWithLogVO;

import java.util.Date;
import java.util.List;

public interface IPriceReminderService {
	
	PageData<PriceReminderWithInfoVO> getAllReminder(int page, int pageSize, Boolean disabled,
			String emIndustry);

	/**
	 * 查询所有监听的股票的行业分布
	 * @return key是行业名称，值是计数
	 */
	List<PriceReminderIndustryCountVO> getIndustryCount();

	/**
	 * 获取关注的股票信息, 排除今日已经发送过邮件的，排除被禁用的
	 * @return
	 */
	List<PriceReminderDO> getReminderRangeWithoutSendMailToday();

	/**
	 * 获取今天没有匹配到的上涨百分比的股票
	 */
	List<PriceReminderDO> getReminderForRisePctWithoutMatchToday();

	/**
	 * 获取今天没有匹配到的下跌百分比的股票
	 */
	List<PriceReminderDO> getReminderForFallPctWithoutMatchToday();

	/**
	 * 获取特别关注的股票，排除今天已经发送过概率告警的
	 */
	List<PriceReminderDO> getReminderWithoutProbabilityMatchToday();

	/**
	 * 查询分数比较高的，且没有在特别关注列表里，且排除今天已经发送过概率告警的
	 * 这里特别排除3xxxxx股票
	 * @return
	 */
	List<String> getHighScoreSymbolsWithoutProbabilityMatchToday();

	/**
	 * 查询当天是否已经发送过
	 * @return true表示已经发送过
	 */
	boolean isSendProbabilityNotice(String symbol, String matchType);

	PriceReminderWithLogVO getBySymbol(String symbol);
	
	/**
	 * 如果关注的价格值为null，则表示清除这个值的关注
	 */
	PriceReminderDO addOrUpdate(PriceReminderDO priceReminderDO);

	boolean enableSpecialReminder(long id, boolean enableSpecialReminder);

	boolean deletePriceReminder(long id);

	boolean deleteAllPriceReminder();
	
	boolean deletePriceReminderMatch(long id);

	boolean deleteAllPriceReminderMatch();
	
	/**
	 * 功能：当股价匹配到时，写入记录。
	 * 支持可重入，当当天已经存在相同类型的匹配记录时，不再接入。
	 */
	boolean addPriceMatch(String symbol, Date matchDate, PriceReminderMatchTypeEnum matchType,
			PriceReminderDO priceReminderDO, DailyDataVO dailyDataVO,
						  double currentPrice, double currentChangePercent, boolean isTodayMatch);
	
	PageData<PriceReminderMatchWithInfoVO> getAllPriceMatch(QueryPriceReminderMatchReq req);
	
	boolean switchReadPriceMatch(long id);
	
	boolean readPriceMatchAll();

}
