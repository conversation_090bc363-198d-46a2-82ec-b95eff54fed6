package com.pugwoo.finance.reminder.service.impl;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.finance.data.model.DailyDataVO;
import com.pugwoo.finance.reminder.dto.QueryPriceReminderMatchReq;
import com.pugwoo.finance.reminder.entity.PriceReminderDO;
import com.pugwoo.finance.reminder.entity.PriceReminderLogDO;
import com.pugwoo.finance.reminder.entity.PriceReminderMatchDO;
import com.pugwoo.finance.reminder.enums.PriceReminderMatchTypeEnum;
import com.pugwoo.finance.reminder.service.IPriceReminderService;
import com.pugwoo.finance.reminder.vo.PriceReminderIndustryCountVO;
import com.pugwoo.finance.reminder.vo.PriceReminderMatchWithInfoVO;
import com.pugwoo.finance.reminder.vo.PriceReminderWithInfoVO;
import com.pugwoo.finance.reminder.vo.PriceReminderWithLogVO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class PriceReminderServiceImpl implements IPriceReminderService {
	
	@Autowired
	private DBHelper dbHelper;

	@Override
	public PageData<PriceReminderWithInfoVO> getAllReminder(int page, int pageSize, Boolean disabled,
			String emIndustry) {
		StringBuilder where = new StringBuilder("where 1=1");
		List<Object> params = new ArrayList<>();
		if(disabled != null) {
			where.append(" and disabled=?");
			params.add(disabled);
		}
		if(StringUtils.isNotBlank(emIndustry)) {
			where.append(" and symbol in (select symbol from t_symbol_china where em_industry=? and deleted=0)");
			params.add(emIndustry);
		}
		return dbHelper.getPage(PriceReminderWithInfoVO.class, page, pageSize,
				where.toString(), params.toArray());
	}

	@Override
	public List<PriceReminderIndustryCountVO> getIndustryCount() {
		return dbHelper.getRaw(PriceReminderIndustryCountVO.class,
				"""
						SELECT t2.`em_industry` AS industry,COUNT(*) AS `count`
						FROM t_price_reminder t1 LEFT JOIN `t_symbol_china` t2
						ON t1.`symbol`=t2.`symbol`
						WHERE t1.`deleted`=0 AND t2.`deleted`=0
						GROUP BY t2.`em_industry`
						ORDER BY `count` DESC
				""");
	}

	@Override
	public List<PriceReminderDO> getReminderRangeWithoutSendMailToday() {
		return dbHelper.getAll(PriceReminderDO.class,
				"""
                    where disabled = 0 and symbol not in (
                        select symbol from t_price_reminder_match where match_type in (?) and match_date=? and deleted=0
                    ) and (target_low>0 or target_high>0 or target_low2>0 or target_high2>0)
                """,
				ListUtils.newList(PriceReminderMatchTypeEnum.THIN.getCode(), PriceReminderMatchTypeEnum.FAT.getCode()),
				     LocalDate.now());
	}

	@Override
	public List<PriceReminderDO> getReminderForRisePctWithoutMatchToday() {
		return dbHelper.getAll(PriceReminderDO.class,
			"""
				where disabled = 0 and symbol not in (
					select symbol from t_price_reminder_match where match_type=? and match_date=? and deleted=0
				) and rise_percent>0
			""", PriceReminderMatchTypeEnum.BEYOND_RISE_PCT.getCode(), LocalDate.now());
	}

	@Override
	public List<PriceReminderDO> getReminderForFallPctWithoutMatchToday() {
		return dbHelper.getAll(PriceReminderDO.class,
			"""
				where disabled = 0 and symbol not in (
					select symbol from t_price_reminder_match where match_type=? and match_date=? and deleted=0
				) and fall_percent>0
			""", PriceReminderMatchTypeEnum.BELOW_FALL_PCT.getCode(), LocalDate.now());
	}

	@Override
	public List<PriceReminderDO> getReminderWithoutProbabilityMatchToday() {
		return dbHelper.getAll(PriceReminderDO.class,
			"""
				where disabled = 0 and symbol not in (
					select symbol from t_price_reminder_match where match_type in (?, ?, ?, ?) and match_date=? and deleted=0
					group by symbol having count(*)=4
				)
			""",
			PriceReminderMatchTypeEnum.VERY_LOW_PROBABILITY_RISE.getCode(),
			PriceReminderMatchTypeEnum.VERY_LOW_PROBABILITY_FALL.getCode(),
			PriceReminderMatchTypeEnum.LOW_PROBABILITY_RISE.getCode(),
			PriceReminderMatchTypeEnum.LOW_PROBABILITY_FALL.getCode(),
			LocalDate.now());
	}

	@Override
	public boolean isSendProbabilityNotice(String symbol, String matchType) {
		return dbHelper.getCount(PriceReminderMatchDO.class,
				"where match_date=? and deleted=0 and match_type=? and symbol=?", LocalDate.now(), matchType, symbol) > 0;
	}

	@Override
	public boolean enableSpecialReminder(long id, boolean enableSpecialReminder) {
		PriceReminderDO priceReminderDO = new PriceReminderDO();
		priceReminderDO.setId(id);
		priceReminderDO.setSpecialReminder(enableSpecialReminder);
		return dbHelper.update(priceReminderDO) > 0;
	}

	@Override
	public PriceReminderDO addOrUpdate(PriceReminderDO priceReminderDO) {
		PriceReminderDO old = null;
		if(priceReminderDO.getId() == null) {
			old = dbHelper.getOne(PriceReminderDO.class, "where symbol=?",
					priceReminderDO.getSymbol());
		    if(old != null) {
		    	priceReminderDO.setId(old.getId());
		    }
		} else {
			old = dbHelper.getByKey(priceReminderDO.getClass(), priceReminderDO.getId());
		}

		if (old == null) {
			dbHelper.insert(priceReminderDO);
		} else {
			if (StringTools.isNotBlank(priceReminderDO.getSymbol())) {
				old.setSymbol(priceReminderDO.getSymbol());
			}
			old.setTargetHigh(priceReminderDO.getTargetHigh());
			old.setTargetLow(priceReminderDO.getTargetLow());
			old.setTargetHigh2(priceReminderDO.getTargetHigh2());
			old.setTargetLow2(priceReminderDO.getTargetLow2());
			old.setRisePercent(priceReminderDO.getRisePercent());
			old.setFallPercent(priceReminderDO.getFallPercent());
			if (priceReminderDO.getDisabled() != null) {
				old.setDisabled(priceReminderDO.getDisabled());
			}
			if (priceReminderDO.getRemark() != null) {
				old.setRemark(priceReminderDO.getRemark());
			}
			if (priceReminderDO.getSpecialReminder() != null) {
				old.setSpecialReminder(priceReminderDO.getSpecialReminder());
			}
			dbHelper.updateWithNull(old); // 支持清除这几个监听的数据
		}

		if(priceReminderDO.getTargetHigh() != null && priceReminderDO.getTargetLow() != null) {
			if(old==null || !(priceReminderDO.getTargetHigh().equals(old.getTargetHigh())
				 && priceReminderDO.getTargetLow().equals(old.getTargetLow()))) {
				PriceReminderLogDO logDO = new PriceReminderLogDO();
				logDO.setSymbol(priceReminderDO.getSymbol());
				logDO.setTargetHigh(priceReminderDO.getTargetHigh());
				logDO.setTargetLow(priceReminderDO.getTargetLow());
				dbHelper.insert(logDO);
			}
		}
		if(priceReminderDO.getTargetHigh2() != null && priceReminderDO.getTargetLow2() != null) {
			if(old==null || !(priceReminderDO.getTargetHigh2().equals(old.getTargetHigh2())
					 && priceReminderDO.getTargetLow2().equals(old.getTargetLow2()))) {
				PriceReminderLogDO logDO = new PriceReminderLogDO();
				logDO.setSymbol(priceReminderDO.getSymbol());
				logDO.setTargetHigh(priceReminderDO.getTargetHigh2());
				logDO.setTargetLow(priceReminderDO.getTargetLow2());
				dbHelper.insert(logDO);
			}
		}
		
		return priceReminderDO;
	}

	@Override
	public boolean deletePriceReminder(long id) {
		return dbHelper.delete(PriceReminderDO.class, "where id=?", id) > 0;
	}

	@Override
	public boolean deleteAllPriceReminder() {
		dbHelper.delete(PriceReminderDO.class, "where 1=1");
		return true;
	}

	@Override
	public boolean deleteAllPriceReminderMatch() {
		dbHelper.delete(PriceReminderMatchDO.class, "where 1=1");
		return true;
	}

	@Override
	public boolean deletePriceReminderMatch(long id) {
		return dbHelper.delete(PriceReminderMatchDO.class, "where id=?", id) > 0;
	}

	@Override
	public boolean addPriceMatch(String symbol, Date matchDate, PriceReminderMatchTypeEnum matchType,
			PriceReminderDO priceReminderDO, DailyDataVO dailyDataVO,
								 double currentPrice, double currentChangePercent, boolean isTodayMatch) {
		PriceReminderMatchDO priceReminderMatchDO = dbHelper.getOne(PriceReminderMatchDO.class,
				"where symbol=? and match_date=? and match_type=?", symbol, matchDate, matchType.getCode());
		if(priceReminderMatchDO != null) {
			return false;
		}
		priceReminderMatchDO = new PriceReminderMatchDO();
		
		priceReminderMatchDO.setSymbol(symbol);
		priceReminderMatchDO.setMatchDate(matchDate);
		priceReminderMatchDO.setMatchType(matchType.getCode());
		priceReminderMatchDO.setIsRead(false);
		priceReminderMatchDO.setTargetHigh(priceReminderDO.getTargetHigh());
		priceReminderMatchDO.setTargetLow(priceReminderDO.getTargetLow());
		priceReminderMatchDO.setRisePercent(priceReminderDO.getRisePercent());
		priceReminderMatchDO.setFallPercent(priceReminderDO.getFallPercent());
		priceReminderMatchDO.setCurPrice(BigDecimal.valueOf(currentPrice));
		priceReminderMatchDO.setCurChangePercent(BigDecimal.valueOf(currentChangePercent));
		priceReminderMatchDO.setRemark(priceReminderDO.getRemark());
		priceReminderMatchDO.setPercentChangeDays(isTodayMatch ? 1 :2);
		
		return dbHelper.insert(priceReminderMatchDO) > 0;
	}

	@Override
	public PageData<PriceReminderMatchWithInfoVO> getAllPriceMatch(QueryPriceReminderMatchReq req) {
		WhereSQL whereSQL = new WhereSQL();
		whereSQL.andIf(req.getIsRead() != null, "is_read=?", req.getIsRead());
		whereSQL.andIf(req.getMatchDate() != null, "match_date=?", req.getMatchDate());
		whereSQL.andIf(StringTools.isNotBlank(req.getMatchType()), "match_type=?", req.getMatchType());
		whereSQL.andIf(StringTools.isNotBlank(req.getSymbol()), "symbol=?", req.getSymbol());
		whereSQL.addOrderBy("create_time desc");

		return dbHelper.getPage(PriceReminderMatchWithInfoVO.class, req.getPage(), req.getPageSize(),
				whereSQL.getSQL(), whereSQL.getParams());
	}

	@Override
	public boolean switchReadPriceMatch(long id) {
		PriceReminderMatchDO matchDO = dbHelper.getByKey(PriceReminderMatchDO.class, id);
        matchDO.setIsRead(matchDO.getIsRead() == null || !matchDO.getIsRead());
		return dbHelper.update(matchDO) > 0;
	}
	
	@Override
	public boolean readPriceMatchAll() {
		dbHelper.updateAll(PriceReminderMatchDO.class, "set is_read=true", "where is_read=false");
		return true;
	}

	@Override
	public PriceReminderWithLogVO getBySymbol(String symbol) {
		if(symbol == null) return null;
		return dbHelper.getOne(PriceReminderWithLogVO.class, "where symbol=?", symbol);
	}

}
