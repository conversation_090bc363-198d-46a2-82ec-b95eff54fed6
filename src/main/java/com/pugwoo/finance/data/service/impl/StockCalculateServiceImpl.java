package com.pugwoo.finance.data.service.impl;

import com.pugwoo.finance.data.model.DailyDataVO;
import com.pugwoo.finance.data.service.StockCalculateService;
import com.pugwoo.finance.utils.algorithm.Probability;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class StockCalculateServiceImpl implements StockCalculateService {

    @Override
    public double calProbability(double currentPrice, List<DailyDataVO> list) {
        if (list == null || list.isEmpty()) {
            return 100;
        }

        // 提取历史股票涨跌幅数据
        List<Double> changeRates = new ArrayList<>();
        for (DailyDataVO dailyData : list) {
            double change = dailyData.getChange(); // 获取涨跌幅百分比
            changeRates.add(change);
        }

        // 计算当前价格对应的涨跌幅
        double currentChange = 0;
        DailyDataVO latestData = list.getFirst(); // 列表是按时间倒序排列的
        if (latestData.getClose() != 0) {
            currentChange = (currentPrice - latestData.getClose()) / latestData.getClose() * 100;
        }

        // 使用Probability工具类计算概率
        return Probability.calProbability(currentChange, changeRates);
    }

    @Override
    public double calProbability2d(double currentPrice, List<DailyDataVO> list) {
        if (list == null || list.isEmpty()) {
            return 100;
        }

        // 提取历史股票涨跌幅数据
        List<Double> changeRates = new ArrayList<>();
        for (DailyDataVO dailyData : list) {
            double change = dailyData.getChange(); // 获取涨跌幅百分比
            changeRates.add(change);
        }

        // 计算当前价格对应2t天涨跌幅
        double currentChange = 0;
        if (list.size() >= 2) {
            DailyDataVO latest2Data = list.get(1); // 列表是按时间倒序排列的
            if (latest2Data.getClose() != 0) {
                currentChange = (currentPrice - latest2Data.getClose()) / latest2Data.getClose() * 100;
            }
        }

        // 使用Probability工具类计算概率
        return Probability.calProbability(currentChange, changeRates);
    }
}
