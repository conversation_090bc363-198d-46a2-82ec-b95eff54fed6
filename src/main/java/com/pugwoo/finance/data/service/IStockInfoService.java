package com.pugwoo.finance.data.service;

import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.finance.data.entity.SymbolChinaDO;
import com.pugwoo.finance.data.entity.SymbolChinaLabelDO;
import com.pugwoo.finance.data.vo.SymbolChinaPDOWithScoreVO;
import com.pugwoo.finance.score.entity.SymbolChinaLabelForScoreDO;
import com.pugwoo.finance.score.vo.SymbolChinaLabelWithInfoVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IStockInfoService {

	/**
	 * 通过股票编号查询
	 */
	SymbolChinaDO getBySymbol(String symbol);
	
	/**
	 * 通过股票名称模糊查询
	 */
	List<SymbolChinaDO> getByName(String name);

	List<SymbolChinaDO> getAllSymbols();

	PageData<SymbolChinaDO> getPage(int page, int pageSize);

	boolean update(SymbolChinaDO symbolChinaDO);

	/**
	 * 插入或更新SymbolChinaLabelDO
	 */
	boolean insertOrUpdate(SymbolChinaLabelDO labelDO);

	boolean insertOrUpdate(SymbolChinaLabelForScoreDO labelDO);

	void hardDeleteSymbolChinaLabelForScoreDO();
	
	/**
	 * 获得所有标签labelGroup的集合
	 */
	List<SymbolChinaLabelDO> getLabelGroupEnum();
	
	/**
	 * 删除labelGroup
	 */
	boolean delLabelGroup(String labelGroup);

	boolean delAllLabelGroup();
	
	/**
	 * 获得所有的标签的枚举值,一个标签是由labelGroup+label标识的
	 */
	List<SymbolChinaLabelDO> getLabelEnum();
	
	/**
	 * 获得指定labelGroup下的所有标签
	 */
	List<SymbolChinaLabelDO> getLabelEnum(String labelGroup);

	/**
	 * 查询group symbol下的1个标签
	 */
	SymbolChinaLabelDO getLabel(String labelGroup, String symbol);

	/**
	 * 查询指定label下的所有股票，如果labelGroup和label有空的，则不加入查询，也即，全部为空则查询全部
	 */
	List<SymbolChinaLabelWithInfoVO> getGroupAllLabels(String labelGroup, String label);

	/**
	 * 查询股票信息，附带评分
	 */
	List<SymbolChinaPDOWithScoreVO> getStockInfoWithScore(List<String> symbols);

	/**
	 * 查询制定的labelGroup的所有label股票列表，按照label来分组
	 */
	Map<String, List<SymbolChinaLabelWithInfoVO>> getGroupedLabels(String labelGroup);
	
	/**
	 * 查询最后一天的股票个数，目前只弄A股
	 */
	Map<String, Object> getNewestDateStockCount();
	
	/**
	 * 查询所有日期的股票个数，按日期时间逆序排列，用的是LinkedHashMap，目前只弄A股
	 */
	Map<Date, Integer> getDateStockCounts();

}
