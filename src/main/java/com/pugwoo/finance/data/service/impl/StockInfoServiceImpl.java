
package com.pugwoo.finance.data.service.impl;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.finance.data.entity.SymbolChinaDO;
import com.pugwoo.finance.data.entity.SymbolChinaLabelDO;
import com.pugwoo.finance.data.enums.MarketEnum;
import com.pugwoo.finance.data.model.DailyDataVO;
import com.pugwoo.finance.data.service.IStockInfoService;
import com.pugwoo.finance.data.service.LocalDailyData;
import com.pugwoo.finance.data.vo.SymbolChinaPDOWithScoreVO;
import com.pugwoo.finance.score.entity.SymbolChinaLabelForScoreDO;
import com.pugwoo.finance.score.vo.SymbolChinaLabelWithInfoVO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class StockInfoServiceImpl implements IStockInfoService {
	
	@Autowired
	private DBHelper dbHelper;

	@Override
	public SymbolChinaDO getBySymbol(String symbol) {
		return dbHelper.getOne(SymbolChinaDO.class, "where symbol=?", symbol);
	}
	
	@Override
	public List<SymbolChinaDO> getByName(String name) {
		return dbHelper.getAll(SymbolChinaDO.class, "where name like ?", "%"+name+"%");
	}

	@Override
	public List<SymbolChinaDO> getAllSymbols() {
		return dbHelper.getAll(SymbolChinaDO.class);
	}

	@Override
	public PageData<SymbolChinaDO> getPage(int page, int pageSize) {
		return dbHelper.getPage(SymbolChinaDO.class, page, pageSize);
	}

	@Override
	public boolean update(SymbolChinaDO symbolChinaDO) {
		return dbHelper.update(symbolChinaDO) > 0;
	}

	@Override
	public List<SymbolChinaLabelDO> getLabelGroupEnum() {
		return dbHelper.getAll(SymbolChinaLabelDO.class, "group by label_group");
	}
	
	@Override
	public boolean delLabelGroup(String labelGroup) {
		if(labelGroup == null) {
			return false;
		}
		dbHelper.delete(SymbolChinaLabelDO.class, "where label_group=?", labelGroup);
		return true;
	}

	@Override
	public boolean delAllLabelGroup() {
		dbHelper.deleteHard(SymbolChinaLabelDO.class, "where label_group like 'level%'");
		return true;
	}

	@Override
	public boolean insertOrUpdate(SymbolChinaLabelDO labelDO) {
		return dbHelper.insertOrUpdate(labelDO) > 0;
	}

	@Override
	public boolean insertOrUpdate(SymbolChinaLabelForScoreDO labelDO) {
		return dbHelper.insertOrUpdate(labelDO) > 0;
	}

	@Override
	public void hardDeleteSymbolChinaLabelForScoreDO() {
		dbHelper.deleteHard(SymbolChinaLabelForScoreDO.class, "where 1=1");
	}

	@Override
	public List<SymbolChinaLabelDO> getLabelEnum() {
		return dbHelper.getAll(SymbolChinaLabelDO.class, "GROUP BY label_group,label");
	}
	
	@Override
	public List<SymbolChinaLabelDO> getLabelEnum(String labelGroup) {
		if(labelGroup == null) {
			return new ArrayList<>();
		}
		return dbHelper.getAll(SymbolChinaLabelDO.class, 
				"where label_group=? GROUP BY label_group,label", labelGroup);
	}
	
	@Override
	public SymbolChinaLabelDO getLabel(String labelGroup, String symbol) {
		return dbHelper.getOne(SymbolChinaLabelDO.class, 
				"where label_group=? and symbol=?", labelGroup, symbol);
	}
	
	@Override
	public List<SymbolChinaLabelWithInfoVO> getGroupAllLabels(String labelGroup, String label) {
		StringBuilder where = new StringBuilder("where 1=1");
		List<Object> params = new ArrayList<>();
		if (StringUtils.isNotBlank(labelGroup)) {
			where.append(" and label_group=?");
			params.add(labelGroup);
		}
		if (StringUtils.isNotBlank(label)) {
			where.append(" and label=?");
			params.add(label);
		}
		return dbHelper.getAll(SymbolChinaLabelWithInfoVO.class,
				where.toString(), params.toArray());
	}

	@Override
	public List<SymbolChinaPDOWithScoreVO> getStockInfoWithScore(List<String> symbols) {
		return dbHelper.getAll(SymbolChinaPDOWithScoreVO.class,
				"where symbol in (?)", symbols);
	}

	@Override
	public Map<String, List<SymbolChinaLabelWithInfoVO>> getGroupedLabels(String labelGroup) {
		List<SymbolChinaLabelWithInfoVO> list = dbHelper.getAll(SymbolChinaLabelWithInfoVO.class,
				"where label_group=?", labelGroup);
		Map<String, List<SymbolChinaLabelWithInfoVO>> map = new HashMap<>();
		for(SymbolChinaLabelWithInfoVO vo : list) {
			if(!map.containsKey(vo.getLabel())) {
				map.put(vo.getLabel(), new ArrayList<>());
			}
			map.get(vo.getLabel()).add(vo);
		}
		return map;
	}
	
	@Override
	public Map<String, Object> getNewestDateStockCount() {
		Set<String> symbols = LocalDailyData.getSymbols(MarketEnum.CHINA);
		Date newestDate = null;
		int count = 0;
		for(String symbol : symbols) {
			List<DailyDataVO> list = LocalDailyData.query(symbol, 1);
			if(!list.isEmpty()) {
				DailyDataVO d = list.get(0);
				if(newestDate == null || d.getDate().after(newestDate)) {
					newestDate = d.getDate();
					count=1;
				} else if(newestDate.equals(d.getDate())) {
					count++;
				}
			}
		}
		Map<String, Object> result = new HashMap<>();
		result.put("date", newestDate);
		result.put("count", count);
		return result;
	}
	
	@Override
	public Map<Date, Integer> getDateStockCounts() {
		Set<String> symbols = LocalDailyData.getSymbols(MarketEnum.CHINA);
		Map<Date, Integer> map = new HashMap<>();
		for(String symbol : symbols) {
			List<DailyDataVO> list = LocalDailyData.query(symbol);
			for(DailyDataVO dailyDataVO : list) {
				if(!map.containsKey(dailyDataVO.getDate())) {
					map.put(dailyDataVO.getDate(), 0);
				}
				map.put(dailyDataVO.getDate(), map.get(dailyDataVO.getDate()) + 1);
			}
		}
		
		List<Date> dates = new ArrayList<>(map.keySet());
		Collections.sort(dates);
		Collections.reverse(dates);
		
		Map<Date, Integer> result = new LinkedHashMap<>();
		for(Date date : dates) {
			result.put(date, map.get(date));
		}
		return result;
	}
	
}
