package com.pugwoo.finance.data.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pugwoo.finance.data.model.RealtimeDataVO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.RegexUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

/**
 * 获取股票实时数据的接口
 */
@Slf4j
public class RealtimeData {

    /**
     * 查询股票的实时价格，目前只支持A股的股票
     * @return null表示查询失败，如果时效超过5分钟，返回null
     */
    public static RealtimeDataVO queryRealtimePrice(String symbol) {
        if (StringTools.isBlank(symbol)) {
            return null;
        }

        RealtimeDataVO realtimeDataVO = new RealtimeDataVO();
        realtimeDataVO.setSymbol(symbol);

        String last3Digit = symbol.substring(symbol.length() - 3);
        String url = "https://hq.stock.sohu.com/cn/" + last3Digit + "/cn_" + symbol + "-1.html?_=" + System.currentTimeMillis();

        Browser browser = new Browser();
        try {
            HttpResponse resp = browser.get(url);
            String body = resp.getContentString("gbk");

            // 解析股价
            String priceTxt = RegexUtils.getFirstMatchStr(body, "quote_k_r',(.*),'quote_wk_r");
            priceTxt = "[" + priceTxt;
            List<String> priceList = JSON.parse(priceTxt, new TypeReference<>() {});
            if (ListUtils.isEmpty(priceList)) {
                log.error("fail to query stock realtime price, url:{}, body:{}", url, body);
                return null;
            }

            List<String> priceList2 = JSON.parse(priceList.getFirst(), new TypeReference<>() {});
            if (priceList2 == null || priceList2.size() < 5) {
                log.error("fail to query stock realtime price, url:{}, body:{}", url, body);
                return null;
            }
            realtimeDataVO.setOpen(Double.parseDouble(priceList2.get(1)));
            realtimeDataVO.setCurrentPrice(Double.parseDouble(priceList2.get(2)));
            realtimeDataVO.setHigh(Double.parseDouble(priceList2.get(3)));
            realtimeDataVO.setLow(Double.parseDouble(priceList2.get(4)));

            // 检查价格信息
            if (realtimeDataVO.getCurrentPrice() == 0.0d) {   // 没有数据
                log.error("fail to query stock realtime price, price == 0, symbol:{}", symbol);
                return null;
            }

            // 解析当前时间
            String timeTxt = RegexUtils.getFirstMatchStr(body, "time':(\\[.*\\]),'news_m_r");
            List<String> timeList = JSON.parse(timeTxt, new TypeReference<>() {});
            if (timeList == null || timeList.size() != 6) {
                log.error("fail to query stock realtime price, url:{}, body:{}", url, body);
                return null;
            }
            Date date = DateUtils.parse(timeList.get(0) + "-" + timeList.get(1) + "-" + timeList.get(2) + " " + timeList.get(3) + ":" + timeList.get(4) + ":" + timeList.get(5));
            if (date == null) {
                log.error("fail to query stock realtime price, url:{}, body:{}", url, body);
                return null;
            }
            realtimeDataVO.setDate(date);

            // 判断是否为当前时间 10 分钟内
            if (System.currentTimeMillis() - realtimeDataVO.getDate().getTime() > 1000L * 60 * 10) {
                log.warn("price realtime is delay more than 10 min, symbol:{}, currentTime:{}, dataTime:{}", symbol,
                        DateUtils.format(new Date()), DateUtils.format(realtimeDataVO.getDate()));
                return null;
            }

        } catch (Exception e) {
            log.error("fail to query stock realtime price, url:{}", url, e);
            return null;
        }

        return realtimeDataVO;
    }

}
