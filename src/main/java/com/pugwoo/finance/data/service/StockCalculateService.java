package com.pugwoo.finance.data.service;

import com.pugwoo.finance.data.model.DailyDataVO;

import java.util.List;

/**
 * 和股票计算相关的服务
 */
public interface StockCalculateService {

    /**
     * 计算当前股价在历史给定的日期里面，出现的概率
     * @return 如果list为空，返回100
     */
    double calProbability(double currentPrice, List<DailyDataVO> list);


    /**
     * 计算当前股价在2天的涨跌幅，在历史给定的日期里面（历史的还是以1天涨跌幅为采样），出现的概率
     * @return 如果list为空，返回100
     */
    double calProbability2d(double currentPrice, List<DailyDataVO> list);

}
