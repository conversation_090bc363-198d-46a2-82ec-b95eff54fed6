package com.pugwoo.rainbow.webdata.service;

import com.pugwoo.rainbow.webdata.entity.WebDataConfLogDO;
import com.pugwoo.rainbow.webdata.entity.WebDataConfigDO;
import org.junit.jupiter.api.Test;

import java.util.Date;

/**
 * WebData失败告警功能测试
 */
public class WebDataFailureAlertTest {

    @Test
    public void testFailureAlertLogic() {
        // 测试告警逻辑
        WebDataConfigDO config = new WebDataConfigDO();
        config.setName("测试任务");
        config.setCode("test_task");
        config.setUrl("http://test.com");
        config.setFailNoticeValve(3); // 失败3次告警
        config.setSendEmail("<EMAIL>"); // 测试邮箱
        config.setEnableCron(false);

        // 创建测试日志，模拟失败次数达到阈值
        WebDataConfLogDO logDO = new WebDataConfLogDO();
        logDO.setWebDataConfId(1L);
        logDO.setStatus("FINISHED");
        logDO.setBeginTime(new Date());
        logDO.setEndTime(new Date());
        logDO.setFailCount(5); // 失败5次，超过阈值3次
        logDO.setSuccessCount(2);
        logDO.setTotalCount(7);
        logDO.setIsSendNotify(false); // 未发送过告警

        // 验证告警条件
        assert config.getFailNoticeValve() != null && config.getFailNoticeValve() > 0;
        assert config.getSendEmail() != null && !config.getSendEmail().trim().isEmpty();
        assert logDO.getFailCount() != null && logDO.getFailCount() >= config.getFailNoticeValve();
        assert logDO.getIsSendNotify() == null || !logDO.getIsSendNotify();

        System.out.println("失败告警逻辑测试通过");
        System.out.println("配置名称: " + config.getName());
        System.out.println("失败阈值: " + config.getFailNoticeValve());
        System.out.println("实际失败次数: " + logDO.getFailCount());
        System.out.println("告警邮箱: " + config.getSendEmail());
    }
}
