package com.pugwoo.finance.data;

import com.pugwoo.finance.data.model.DailyDataVO;
import com.pugwoo.finance.data.service.LocalDailyData;
import com.pugwoo.finance.data.service.StockCalculateService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class TestStockCalculateService {

    @Resource
    private StockCalculateService stockCalculateService;

    @Test
    public void testCalProbability() {
        String symbol = "002230";
        List<DailyDataVO> list = LocalDailyData.query(symbol, 250);
        double probability = stockCalculateService.calProbability(47.47, list);
        System.out.println(probability);
    }

}
