package com.pugwoo.finance.reminder;

import com.pugwoo.finance.reminder.service.IPriceReminderService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class TestPriceReminderProbability {

    @Resource
    private IPriceReminderService priceReminderService;

    @Test
    public void testGetSpecialReminderWithoutProbabilityMatchToday() {
        var result = priceReminderService.getSpecialReminderWithoutProbabilityMatchToday();
        System.out.println("Found " + result.size() + " special reminder stocks");
        result.forEach(stock -> {
            System.out.println("Symbol: " + stock.getSymbol() + 
                ", Special Reminder: " + stock.getSpecialReminder() + 
                ", Disabled: " + stock.getDisabled());
        });
    }
}
